# Read elevation raster
# ----------------------------
from pysheds.grid import Grid
import matplotlib.pyplot as plt
from matplotlib import colors
# import seaborn as sns

grid = Grid.from_raster('elevation.tiff')
dem = grid.read_raster('elevation.tiff')

# import numpy as np
# import matplotlib.pyplot as plt
# from matplotlib import colors
# import seaborn as sns

# fig, ax = plt.subplots(figsize=(8,6))
# fig.patch.set_alpha(0)

# plt.imshow(dem, extent=grid.extent, cmap='terrain', zorder=1)
# plt.colorbar(label='Elevation (m)')
# plt.grid(zorder=0)
# plt.title('Digital elevation map', size=14)
# plt.xlabel('Longitude')
# plt.ylabel('Latitude')
# plt.tight_layout()
# plt.show()

# Condition DEM
# ----------------------
# Fill pits in DEM
pit_filled_dem = grid.fill_pits(dem)

# Fill depressions in DEM
flooded_dem = grid.fill_depressions(pit_filled_dem)
    
# Resolve flats in DEM
inflated_dem = grid.resolve_flats(flooded_dem)

# Determine D8 flow directions from DEM
# ----------------------
# Specify directional mapping
dirmap = (64, 128, 1, 2, 4, 8, 16, 32)
    
# Compute flow directions
# -------------------------------------
fdir = grid.flowdir(inflated_dem, dirmap=dirmap)

# fig = plt.figure(figsize=(8,6))
# fig.patch.set_alpha(0)

# plt.imshow(fdir, extent=grid.extent, cmap='viridis', zorder=2)
# boundaries = ([0] + sorted(list(dirmap)))
# plt.colorbar(boundaries= boundaries,
#              values=sorted(dirmap))
# plt.xlabel('Longitude')
# plt.ylabel('Latitude')
# plt.title('Flow direction grid', size=14)
# plt.grid(zorder=-1)
# plt.tight_layout()
# plt.show()

# Calculate flow accumulation
# --------------------------
acc = grid.accumulation(fdir, dirmap=dirmap, nprocs=12)

fig, ax = plt.subplots(figsize=(8,6))
fig.patch.set_alpha(0)
plt.grid('on', zorder=0)
im = ax.imshow(acc, extent=grid.extent, zorder=2,
               cmap='cubehelix',
               norm=colors.LogNorm(1, acc.max()),
               interpolation='bilinear')
plt.colorbar(im, ax=ax, label='Upstream Cells')
plt.title('Flow Accumulation', size=14)
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.tight_layout()
plt.show()