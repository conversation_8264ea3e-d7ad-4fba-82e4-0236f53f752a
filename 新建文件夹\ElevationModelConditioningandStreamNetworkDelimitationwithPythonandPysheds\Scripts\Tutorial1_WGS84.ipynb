{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#Import required libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as colors\n", "import geopandas as gpd\n", "from pysheds.grid import Grid\n", "import mplleaflet\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#Open a digital elevation model \n", "grid = Grid.from_raster('../Rst/20190109125130_1063922483.tif', data_name='dem')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["#Define a function to plot the digital elevation model \n", "def plotFigure(data, label, cmap='Blues'):\n", "    plt.figure(figsize=(12,10))\n", "    plt.imshow(data, extent=grid.extent)\n", "    plt.colorbar(label=label)\n", "    plt.grid()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'sGrid' object has no attribute 'dem'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m#<PERSON><PERSON> slicing on borders to enhance colobars\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m elevDem\u001b[38;5;241m=\u001b[39m\u001b[43mgrid\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdem\u001b[49m[:\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m,:\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "\u001b[1;31mAttributeError\u001b[0m: 'sGrid' object has no attribute 'dem'"]}], "source": ["#<PERSON><PERSON> slicing on borders to enhance colobars\n", "elevDem=grid.dem[:-1,:-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plotFigure(elevDem, 'Elevation (m)')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detect depressions\n", "\n", "# Detect depressions\n", "depressions = grid.detect_depressions('dem')\n", "\n", "# Plot depressions\n", "plt.imshow(depressions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fill depressions\n", "grid.fill_depressions(data='dem', out_name='flooded_dem')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test result\n", "depressions = grid.detect_depressions('flooded_dem')\n", "plt.imshow(depressions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detect flats\n", "flats = grid.detect_flats('flooded_dem')\n", "\n", "# Plot flats\n", "plt.imshow(flats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grid.resolve_flats(data='flooded_dem', out_name='inflated_dem')\n", "plt.imshow(grid.inflated_dem[:-1,:-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a flow direction grid\n", "#N    NE    E    SE    S    SW    W    NW\n", "dirmap = (64,  128,  1,   2,    4,   8,    16,  32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grid.flowdir(data='inflated_dem', out_name='dir', dirmap=dirmap)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plotFigure(grid.dir,'Flow Direction','viridis')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Specify discharge point\n", "x, y = -107.91663,27.83479"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Delineate the catchment\n", "grid.catchment(data='dir', x=x, y=y, dirmap=dirmap, out_name='catch',\n", "               recursionlimit=15000, xytype='label', nodata_out=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clip the bounding box to the catchment\n", "grid.clip_to('catch')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get a view of the catchment\n", "demView = grid.view('dem', nodata=np.nan)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plotFigure(dem<PERSON><PERSON><PERSON>,'Elevation')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#export selected raster\n", "grid.to_raster(dem<PERSON><PERSON>w, '../Output/clippedElevations_WGS84.tif')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the stream network\n", "\n", "grid.accumulation(data='catch', dirmap=dirmap, pad_inplace=False, out_name='acc')\n", "\n", "accView = grid.view('acc', nodata=np.nan)\n", "plotFigure(acc<PERSON><PERSON>w,\"Cell Number\",'PuRd')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["streams = grid.extract_river_network('catch', 'acc', threshold=200, dirmap=dirmap)\n", "streams[\"features\"][:2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def saveDict(dic,file):\n", "    f = open(file,'w')\n", "    f.write(str(dic))\n", "    f.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#save geo<PERSON><PERSON> as separate file\n", "saveDict(streams,'../Output/streams_WGS84.geojson')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Some functions to plot the json on jupyter notebook\n", "streamNet = gpd.read_file('../Output/streams_WGS84.geojson')\n", "streamNet.crs = {'init' :'epsg:4326'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The polygonize argument defaults to the grid mask when no arguments are supplied\n", "shapes = grid.polygonize()\n", "\n", "# Plot catchment boundaries\n", "fig, ax = plt.subplots(figsize=(6.5, 6.5))\n", "\n", "for shape in shapes:\n", "    coords = np.asarray(shape[0]['coordinates'][0])\n", "    ax.plot(coords[:,0], coords[:,1], color='cyan')\n", "    \n", "ax.set_xlim(grid.bbox[0], grid.bbox[2])\n", "ax.set_ylim(grid.bbox[1], grid.bbox[3])\n", "ax.set_title('Catchment boundary (vector)')\n", "gpd.plotting.plot_dataframe(streamNet, None, cmap='Blues', ax=ax)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#ax = streamNet.plot()\n", "mplleaflet.display(fig=ax.figure, crs=streamNet.crs, tiles='esri_aerial')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tensorflow", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}